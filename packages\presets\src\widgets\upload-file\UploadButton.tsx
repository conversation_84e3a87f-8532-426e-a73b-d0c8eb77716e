import { Tooltip, message } from "antd";
import React, { useRef } from "react";

import { PresetsCommand } from "@/command";
import { useCommandRunner } from "@cscs-agent/core";
import { Icon } from "@cscs-agent/icons";

interface UploadButtonProps {
  accept: string[];
}

const UploadButton: React.FC<UploadButtonProps> = (props) => {
  const { accept = [] } = props;
  const runner = useCommandRunner();
  const inputRef = useRef<HTMLInputElement | null>(null);
  const acceptStr = accept.join();

  const handleChange: React.ChangeEventHandler<HTMLInputElement> = (e) => {
    const files = filterAccept(e.target.files);
    if (!files) return;
    runner(PresetsCommand.UploadFiles, { files });
    runner(PresetsCommand.OpenUploadFileList);
    // 清空 input
    setTimeout(() => {
      if (inputRef.current) {
        inputRef.current.value = "";
      }
    });
  };

  const filterAccept = (files: FileList | null) => {
    // 文件数量不能超过10个
    if (!files) return;
    const acceptFiles: File[] = [];
    const notAcceptFiles: File[] = [];
    for (let i = 0; i < files.length; i++) {
      const file = files[i];
      const extension = file.name.split(".").pop()?.toLowerCase() ?? "unknown";
      if (accept.includes("." + extension)) {
        acceptFiles.push(file);
      } else {
        notAcceptFiles.push(file);
      }
    }

    if (notAcceptFiles.length > 0) {
      message.warn(`以下文件格式不支持上传：${notAcceptFiles.map((f) => f.name).join(", ")}`);
    }

    return acceptFiles;
  };

  return (
    <Tooltip title="最多10个文件（最大100MB）,支持jpg、jepg、png等图片格式；PDF、Word、Excel、Markdown等文件格式">
      <div className="pts:relative pts:flex pts:justify-center pts:items-center pts:hover:bg-gray-100 pts:rounded-lg pts:w-8 pts:h-8">
        <Icon icon="Attachment" />
        <input
          type="file"
          onChange={handleChange}
          ref={inputRef}
          multiple={true}
          accept={acceptStr}
          className="pts:top-0 pts:left-0 pts:absolute pts:opacity-0 pts:w-8 pts:h-8 pts:cursor-pointer"
        />
      </div>
    </Tooltip>
  );
};

export default UploadButton;
